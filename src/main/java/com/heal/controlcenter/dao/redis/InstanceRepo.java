package com.heal.controlcenter.dao.redis;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.controlcenter.beans.ComponentInstanceBean;
import com.heal.controlcenter.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Repository
@Slf4j
public class InstanceRepo {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RedisUtilities redisUtilities;

    String ACCOUNTS_KEY = "/accounts";
    String INSTANCES_KEY = "/instances";
    String KPI_KEY = "/kpis";
    String ACCOUNTS_HASH = "ACCOUNTS";
    String INSTANCES_HASH = "_INSTANCES_";
    String KPIS_HASH = "_KPIS";

    public void updateKpiDetails(String accountIdentifier, String instanceIdentifier, List<CompInstKpiEntity> bean) {
        log.info("Updating kpi details for accountId: {}, instance: {}", accountIdentifier, instanceIdentifier);
        try {
            redisUtilities.updateKey(ACCOUNTS_KEY + "/" + accountIdentifier + INSTANCES_KEY + "/" + instanceIdentifier + KPI_KEY,
                    ACCOUNTS_HASH + "_" + accountIdentifier + INSTANCES_HASH + instanceIdentifier + KPIS_HASH, bean);
        } catch (Exception e) {
            log.error("Error occurred while updating kpi details for accountId:{}, instance:{}", accountIdentifier, instanceIdentifier, e);
        }
    }

    public void updateKpiDetailsForKpiIdentifier(String accountIdentifier, String instanceIdentifier, CompInstKpiEntity kpiEntity) {
        log.info("Updating kpi details for accountId: {}, instance: {}, kpiId: {}", accountIdentifier, instanceIdentifier, kpiEntity.getId());
        try {
            redisUtilities.updateKey(ACCOUNTS_KEY + "/" + accountIdentifier + INSTANCES_KEY + "/" + instanceIdentifier + KPI_KEY + "/" + kpiEntity.getIdentifier(),
                    ACCOUNTS_HASH + "_" + accountIdentifier + INSTANCES_HASH + instanceIdentifier + KPIS_HASH + "_" + kpiEntity.getIdentifier(), kpiEntity);
        } catch (Exception e) {
            log.error("Error occurred while updating kpi details for accountId:{}, instance:{}", accountIdentifier, instanceIdentifier, e);
        }
    }

    public void updateKpiDetailsForKpiId(String accountIdentifier, String instanceIdentifier, CompInstKpiEntity kpiEntity) {
        log.info("Updating kpi details for accountId: {}, instance: {}, kpiIdentifier: {}", accountIdentifier, instanceIdentifier, kpiEntity.getId());
        try {
            redisUtilities.updateKey(ACCOUNTS_KEY + "/" + accountIdentifier + INSTANCES_KEY + "/" + instanceIdentifier + KPI_KEY + "/" + kpiEntity.getId(),
                    ACCOUNTS_HASH + "_" + accountIdentifier + INSTANCES_HASH + instanceIdentifier + KPIS_HASH + "_" + kpiEntity.getId(), kpiEntity);
        } catch (Exception e) {
            log.error("Error occurred while updating kpi details for accountId:{}, instance:{}", accountIdentifier, instanceIdentifier, e);
        }
    }

    public List<CompInstKpiEntity> getInstanceWiseKpis(String accountIdentifier, String instanceIdentifier) {
        log.info("Fetching instance wise kpi details for accountId: {}, instance: {}", accountIdentifier, instanceIdentifier);
        try {
            String instanceKpiObject = redisUtilities.getKey(ACCOUNTS_KEY + "/" + accountIdentifier + INSTANCES_KEY + "/" + instanceIdentifier + KPI_KEY,
                    ACCOUNTS_HASH + "_" + accountIdentifier + INSTANCES_HASH + instanceIdentifier + KPIS_HASH);
            if (instanceKpiObject == null) {
                log.debug("Instance Kpi details not found for account: [{}]", accountIdentifier);
                return Collections.emptyList();
            }
            return objectMapper.readValue(instanceKpiObject, new TypeReference<List<CompInstKpiEntity>>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting  instance wise kpi details for account [{}]. Details: ", accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    /**
     * Stores component instance details in Redis cache.
     * @param accountIdentifier The account identifier
     * @param instanceDetails The component instance details to store
     */
    public void updateInstanceDetails(String accountIdentifier, CompInstClusterDetails instanceDetails) {
        log.info("Updating instance details for accountId: {}, instance: {}", accountIdentifier, instanceDetails.getIdentifier());
        try {
            String instanceKey = ACCOUNTS_KEY + "/" + accountIdentifier + INSTANCES_KEY + "/" + instanceDetails.getIdentifier();
            String instanceHashKey = ACCOUNTS_HASH + "_" + accountIdentifier + INSTANCES_HASH + instanceDetails.getIdentifier();

            redisUtilities.updateKey(instanceKey, instanceHashKey, instanceDetails);
            log.debug("Successfully updated instance details in Redis for instance: {}", instanceDetails.getIdentifier());
        } catch (Exception e) {
            log.error("Error occurred while updating instance details for accountId:{}, instance:{}",
                    accountIdentifier, instanceDetails.getIdentifier(), e);
        }
    }

    /**
     * Updates the list of all instances for an account in Redis.
     * @param accountIdentifier The account identifier
     * @param instances List of all component instances for the account
     */
    public void updateInstances(String accountIdentifier, List<CompInstClusterDetails> instances) {
        log.info("Updating instances list for accountId: {}, count: {}", accountIdentifier, instances.size());
        try {
            String instancesKey = ACCOUNTS_KEY + "/" + accountIdentifier + INSTANCES_KEY;
            String instancesHashKey = ACCOUNTS_HASH + "_" + accountIdentifier + INSTANCES_HASH;

            redisUtilities.updateKey(instancesKey, instancesHashKey, instances);
            log.debug("Successfully updated instances list in Redis for account: {}", accountIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while updating instances list for accountId:{}", accountIdentifier, e);
        }
    }

    /**
     * Retrieves all component instances for an account from Redis.
     * @param accountIdentifier The account identifier
     * @return List of component instances
     */
    public List<CompInstClusterDetails> getInstances(String accountIdentifier) {
        log.info("Fetching instances for accountId: {}", accountIdentifier);
        try {
            String instancesKey = ACCOUNTS_KEY + "/" + accountIdentifier + INSTANCES_KEY;
            String instancesHashKey = ACCOUNTS_HASH + "_" + accountIdentifier + INSTANCES_HASH;

            String instancesObject = redisUtilities.getKey(instancesKey, instancesHashKey);
            if (instancesObject == null) {
                log.debug("Instances not found for account: [{}]", accountIdentifier);
                return Collections.emptyList();
            }
            return objectMapper.readValue(instancesObject, new TypeReference<List<CompInstClusterDetails>>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting instances for account [{}]. Details: ", accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    /**
     * Retrieves a specific component instance by identifier from Redis.
     * @param accountIdentifier The account identifier
     * @param instanceIdentifier The instance identifier
     * @return Component instance details or null if not found
     */
    public CompInstClusterDetails getInstanceDetailByIdentifier(String accountIdentifier, String instanceIdentifier) {
        log.info("Fetching instance details for accountId: {}, instance: {}", accountIdentifier, instanceIdentifier);
        try {
            String instanceKey = ACCOUNTS_KEY + "/" + accountIdentifier + INSTANCES_KEY + "/" + instanceIdentifier;
            String instanceHashKey = ACCOUNTS_HASH + "_" + accountIdentifier + INSTANCES_HASH + instanceIdentifier;

            String instanceObject = redisUtilities.getKey(instanceKey, instanceHashKey);
            if (instanceObject == null) {
                log.debug("Instance details not found for account: [{}], instance: [{}]", accountIdentifier, instanceIdentifier);
                return null;
            }
            return objectMapper.readValue(instanceObject, CompInstClusterDetails.class);
        } catch (Exception e) {
            log.error("Error occurred while getting instance details for account [{}], instance [{}]. Details: ",
                    accountIdentifier, instanceIdentifier, e);
            return null;
        }
    }

    /**
     * Updates instance details by identifier in Redis.
     * @param accountIdentifier The account identifier
     * @param instanceDetails The updated instance details
     */
    public void updateInstanceByIdentifier(String accountIdentifier, CompInstClusterDetails instanceDetails) {
        log.info("Updating instance by identifier for accountId: {}, instance: {}", accountIdentifier, instanceDetails.getIdentifier());
        try {
            String instanceKey = ACCOUNTS_KEY + "/" + accountIdentifier + INSTANCES_KEY + "/" + instanceDetails.getIdentifier();
            String instanceHashKey = ACCOUNTS_HASH + "_" + accountIdentifier + INSTANCES_HASH + instanceDetails.getIdentifier();

            redisUtilities.updateKey(instanceKey, instanceHashKey, instanceDetails);
            log.debug("Successfully updated instance by identifier in Redis for instance: {}", instanceDetails.getIdentifier());
        } catch (Exception e) {
            log.error("Error occurred while updating instance by identifier for accountId:{}, instance:{}",
                    accountIdentifier, instanceDetails.getIdentifier(), e);
        }
    }

    /**
     * Removes a component instance from Redis cache.
     * @param accountIdentifier The account identifier
     * @param instanceIdentifier The instance identifier to remove
     */
    public void removeInstance(String accountIdentifier, String instanceIdentifier) {
        log.info("Removing instance for accountId: {}, instance: {}", accountIdentifier, instanceIdentifier);
        try {
            String instanceKey = ACCOUNTS_KEY + "/" + accountIdentifier + INSTANCES_KEY + "/" + instanceIdentifier;
            String instanceHashKey = ACCOUNTS_HASH + "_" + accountIdentifier + INSTANCES_HASH + instanceIdentifier;

            redisUtilities.deleteKey(instanceKey, instanceHashKey);
            log.debug("Successfully removed instance from Redis for instance: {}", instanceIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while removing instance for accountId:{}, instance:{}",
                    accountIdentifier, instanceIdentifier, e);
        }
    }

    /**
     * Updates instance-wise services in Redis - equivalent to updateInstanceWiseServices from appsone-controlcenter.
     * @param accountIdentifier The account identifier
     * @param instanceIdentifier The instance identifier
     * @param serviceDetails List of service details for the instance
     */
    public void updateInstanceWiseServices(String accountIdentifier, String instanceIdentifier, List<com.heal.configuration.pojos.BasicEntity> serviceDetails) {
        log.info("Updating instance-wise services for accountId: {}, instance: {}", accountIdentifier, instanceIdentifier);
        try {
            String serviceKey = ACCOUNTS_KEY + "/" + accountIdentifier + INSTANCES_KEY + "/" + instanceIdentifier + "/services";
            String serviceHashKey = ACCOUNTS_HASH + "_" + accountIdentifier + INSTANCES_HASH + instanceIdentifier + "_SERVICES";

            redisUtilities.updateKey(serviceKey, serviceHashKey, serviceDetails);
            log.debug("Successfully updated instance-wise services in Redis for instance: {}", instanceIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while updating instance-wise services for accountId:{}, instance:{}",
                    accountIdentifier, instanceIdentifier, e);
        }
    }

    /**
     * Updates instance attribute details in Redis - equivalent to updateAttributeDetails from appsone-controlcenter.
     * @param accountIdentifier The account identifier
     * @param instanceIdentifier The instance identifier
     * @param instanceAttributes List of instance attributes
     */
    public void updateAttributeDetails(String accountIdentifier, String instanceIdentifier, List<com.heal.configuration.pojos.InstanceAttributes> instanceAttributes) {
        log.info("Updating attribute details for accountId: {}, instance: {}", accountIdentifier, instanceIdentifier);
        try {
            String attributeKey = ACCOUNTS_KEY + "/" + accountIdentifier + INSTANCES_KEY + "/" + instanceIdentifier + "/attributes";
            String attributeHashKey = ACCOUNTS_HASH + "_" + accountIdentifier + INSTANCES_HASH + instanceIdentifier + "_ATTRIBUTES";

            redisUtilities.updateKey(attributeKey, attributeHashKey, instanceAttributes);
            log.debug("Successfully updated attribute details in Redis for instance: {}", instanceIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while updating attribute details for accountId:{}, instance:{}",
                    accountIdentifier, instanceIdentifier, e);
        }
    }
}
