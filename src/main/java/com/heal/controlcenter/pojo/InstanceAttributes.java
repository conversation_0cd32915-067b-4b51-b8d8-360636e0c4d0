package com.heal.controlcenter.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Instance attributes POJO for Redis operations.
 * Equivalent to InstanceAttributes from appsone-controlcenter.
 * Used for storing component instance attribute details in Redis cache.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstanceAttributes {
    private int id;
    private String attributeName;
    private String attributeValue;
    private int compInstanceId;
    private int mstCommonAttributesId;
    private int mstComponentAttributeMappingId;
    private String createdTime;
    private String updatedTime;
    private String userDetailsId;
}
